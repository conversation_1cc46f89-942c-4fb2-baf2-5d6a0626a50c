import { useState } from 'react';
import classnames from 'classnames';
import {
  ConversationItem,
  GroupMemberRole,
  MessageReceiveOptType,
  GroupItem,
  GroupAtType,
} from '@ht/openim-wasm-client-sdk';
import { useConversationSettings } from '@/hooks/useConversationSettings';
import { feedbackToast } from '@/utils/common';
import { message, Modal } from '@ht/sprite-ui';
import { t } from 'i18next';
import { IMSDK } from '@/layouts/BasicLayout';
import { useConversationStore, useUserStore } from '@/store';
import { getCurrentWindow } from '@tauri-apps/api/window';
import { platform } from '@tauri-apps/plugin-os';
import styles from './index.less';

interface IChannelMenuProps {
  conversation: ConversationItem;
  handleClose: () => void;
  showAnnouncementModal: (val: any) => void;
}
const ChannelMenu = ({
  conversation,
  handleClose,
  showAnnouncementModal,
}: IChannelMenuProps) => {
  const {
    updateConversationMessageRemind,
    updateConversationPin,
    handleConversationRemove,
    currentMemberIfJoinedGroup,
    currentMemberIsGroupOwner,
  } = useConversationSettings(conversation);
  const syncState = useUserStore((state) => state.syncState);

  const { userID: selfUserID } = useUserStore.getState().selfInfo;
  // const [groupInfo, setGroupInfo] = useState<GroupItem | undefined>(undefined);
  // const [showGroupAnnouncement, setShowGroupAnnouncement] = useState(false);
  // const [isAdmin, setIsAdmin] = useState(false);
  const isGroup = conversation?.groupID != null && conversation?.groupID !== '';

  const togglePin = () => {
    handleClose();
    updateConversationPin(!conversation?.isPinned);
  };

  const toggleRecvMsgOpt = () => {
    handleClose();
    if (conversation.recvMsgOpt === MessageReceiveOptType.Normal) {
      updateConversationMessageRemind(true, MessageReceiveOptType.NotNotify);
    } else {
      updateConversationMessageRemind(false);
    }
  };

  const handleLeaveGroup = () => {
    Modal.confirm({
      content: t('placeholder.exitGroupToast'),
      onOk: async () => {
        try {
          handleClose();
          await IMSDK.quitGroup(conversation?.groupID);
        } catch (error) {
          feedbackToast({ msg: t('toast.exitGroupFailed'), error });
          console.error('quitGroup error', error);
        }
      },
    });
  };

  const handleClearConversationMessages = () => {
    handleClearConversationMessages();
    handleClose();
  };

  const handleMarkAsRead = () => {
    checkConversationState();
    handleClose();
  };

  // 临时bugfix0717，后面还是要挪到useConversationState中
  const checkConversationState = async () => {
    if (
      !conversation ||
      syncState === 'loading' ||
      document.visibilityState === 'hidden'
    ) {
      return;
    }

    if (conversation.unreadCount > 0) {
      await IMSDK.markConversationMessageAsRead(conversation.conversationID);
      if (window.__TAURI_INTERNALS__) {
        const currentPlatform = platform();
        if (currentPlatform === 'macos') {
          const { unReadCount } = useConversationStore.getState();
          getCurrentWindow().setBadgeCount(
            unReadCount === 0 ? undefined : unReadCount + 1
          );
        }
      }
    }
    const { groupAtType, groupTag = 0 } = conversation;

    if (
      groupAtType === GroupAtType.AtAllAtMe ||
      groupAtType === GroupAtType.AtMe ||
      groupAtType === GroupAtType.AtAll
    ) {
      IMSDK.setConversation({
        conversationID: conversation.conversationID,
        groupAtType: 0,
      });
    }
    if (groupTag === 1) {
      IMSDK.setConversation({
        conversationID: conversation.conversationID,
        groupTag: 0,
      });
    }
  };

  const groupAnnouncementBtn = async () => {
    try {
      const { data: groupList } = await IMSDK.getSpecifiedGroupsInfo([
        conversation.groupID,
      ]);
      const groupItem = groupList[0];
      // setGroupInfo(groupItem);
      const { data } = await IMSDK.getGroupMemberOwnerAndAdmin(
        conversation.groupID
      );

      const isAdminOrOwner = data.some((item) => item.userID === selfUserID);
      if (isAdminOrOwner) {
        showAnnouncementModal({
          isAdmin: true,
          groupInfo: groupItem,
        });
        // setIsAdmin(true);
        // setShowGroupAnnouncement(true);
      } else if (groupItem?.notification) {
        // setIsAdmin(false);
        // setShowGroupAnnouncement(true);
        showAnnouncementModal({
          isAdmin: false,
          groupInfo: groupItem,
        });
      } else {
        message.info('仅群主和管理员可发布群公告');
      }
      handleClose();
    } catch (error) {
      console.error(error);
    }
  };

  return (
    <div
      className={styles.channelMenuContainer}
      onClick={(e) => {
        e.preventDefault();
        e.stopPropagation();
      }}
    >
      {conversation?.unreadCount > 0 && (
        <div className={styles.menuItemButton} onClick={handleMarkAsRead}>
          {t('placeholder.markAsRead')}
        </div>
      )}
      {isGroup && currentMemberIfJoinedGroup && (
        <div className={styles.menuItemButton} onClick={groupAnnouncementBtn}>
          {t('placeholder.groupAnnouncement')}
        </div>
      )}
      <div className={styles.menuItemButton} onClick={toggleRecvMsgOpt}>
        {conversation?.recvMsgOpt
          ? t('placeholder.messageToast')
          : t('placeholder.notNotify')}
      </div>
      <div className={styles.menuSeparator} />
      <div className={styles.menuItemButton} onClick={togglePin}>
        {conversation?.isPinned
          ? t('placeholder.removeSticky')
          : t('placeholder.sticky')}
      </div>
      <div className={styles.menuItemButton} onClick={handleConversationRemove}>
        {t('placeholder.removeConversation')}
      </div>
      {isGroup && currentMemberIfJoinedGroup && !currentMemberIsGroupOwner && (
        <div className={styles.menuSeparator} />
      )}
      {/* <div
        className={styles.menuItemButton}
        onClick={clearConversationMessages}
      >
        {t('toast.clearChatHistory')}
      </div> */}
      {isGroup && currentMemberIfJoinedGroup && !currentMemberIsGroupOwner && (
        <div
          className={classnames(styles.menuItemButton, styles.exit)}
          onClick={handleLeaveGroup}
        >
          {t('placeholder.exitGroup')}
        </div>
      )}
      {/* {showGroupAnnouncement && (
        <GroupAnnouncementModal
          visible={showGroupAnnouncement}
          isAdmin={isAdmin}
          onCancel={() => setShowGroupAnnouncement(false)}
          groupInfo={groupInfo}
          type={groupInfo?.notification ? 'preview' : 'edit'}
        />
      )} */}
    </div>
  );
};
export default ChannelMenu;
