/* eslint-disable eslint-comments/disable-enable-pair */
/* eslint-disable max-statements */

export const replaceEmoji2Str = (text: string) => {
  const parser = new DOMParser();
  const doc = parser.parseFromString(text, 'text/html');

  const emojiEls: HTMLImageElement[] = Array.from(
    doc.querySelectorAll('.emojione')
  );
  emojiEls.forEach((face) => {
    const escapedOut = face.outerHTML.replace(/[-/\\^$*+?.()|[\]{}]/g, '\\$&');
    text = text.replace(new RegExp(escapedOut, 'g'), face.alt);
  });
  return text;
};

export const getCleanText = (html: string) => {
  let text = replaceEmoji2Str(html);
  text = text.replace(/<\/p><p>/g, '\n');
  // text = text.replace(/<br\s*[/]?>/gi, '\n');
  // text = text.replace(/<[^>]+>/g, '');
  text = convertChar(text);
  text = decodeHtmlEntities(text);
  text = text.replace(/\n\s*\n/g, '\n');
  // text = text.replace(/<(https?:\/\/[^>]+)>/g, '$1') || ''

  // 处理空列表和空引用
  const lines = text.split('\n');

  // 检查是否为代码块的开始或结束
  const isCodeBlockMarker = (line: string): boolean =>
    line.trim().startsWith('```');

  // 标记代码块的开始和结束
  let insideCodeBlock = false;
  const markedLines = lines.map((line, index) => {
    if (isCodeBlockMarker(line)) {
      insideCodeBlock = !insideCodeBlock;
      // 如果是代码块的开始，检查后面是否有内容
      if (insideCodeBlock) {
        // 查找下一个非空行
        let nextNonEmptyLineIndex = index + 1;
        while (
          nextNonEmptyLineIndex < lines.length &&
          lines[nextNonEmptyLineIndex].trim() === ''
        ) {
          nextNonEmptyLineIndex++;
        }
        // 如果找到了非空行，并且不是代码块结束标记，则保留当前行
        if (
          nextNonEmptyLineIndex < lines.length &&
          !isCodeBlockMarker(lines[nextNonEmptyLineIndex])
        ) {
          return { line, keep: true, isCodeBlock: true };
        }
      } else {
        // 如果是代码块的结束，检查前面是否有内容
        // 查找上一个非空行
        let prevNonEmptyLineIndex = index - 1;
        while (
          prevNonEmptyLineIndex >= 0 &&
          lines[prevNonEmptyLineIndex].trim() === ''
        ) {
          prevNonEmptyLineIndex--;
        }
        // 如果找到了非空行，并且不是代码块开始标记，则保留当前行
        if (
          prevNonEmptyLineIndex >= 0 &&
          !isCodeBlockMarker(lines[prevNonEmptyLineIndex])
        ) {
          return { line, keep: true, isCodeBlock: true };
        }
      }
    }

    return { line, keep: false, isCodeBlock: insideCodeBlock };
  });

  const cleanedLines = lines.filter((line, index) => {
    const trimmedLine = line.trim();
    const { keep, isCodeBlock } = markedLines[index];

    // 如果是在代码块内部或者被标记为保留的代码块标记，直接保留
    if (isCodeBlock || keep) {
      return true;
    }

    // 如果是空行，直接过滤
    if (trimmedLine === '') {
      return false;
    }

    // 添加 URL 检测
    const urlPattern = /^https?:\/\/[^\s]+$/;
    if (urlPattern.test(trimmedLine)) {
      return true;
    }

    // 检查是否只包含 Markdown 语法字符和空白字符
    // 这个正则会匹配任意组合的 Markdown 语法字符
    if (
      /^([>]+\s*([*#\-+\[\]|`]+\s*)*|[*#\-+\[\]|`]+\s*([>]+\s*)*|\d+\.\s+[>*#\-+\[\]|`\s]*)*$/.test(
        trimmedLine
      ) || // 匹配任意组合的语法字符，包括引用符号的组合
      /^(\d+\.\s*[>\s]*)+$/.test(trimmedLine) || // 匹配有序列表和引用的交替组合
      /^([>\s]*\d+\.\s*)+$/.test(trimmedLine) || // 匹配引用和有序列表的交替组
      trimmedLine === '*'
    ) {
      return false;
    }
    return true;
  });
  text = cleanedLines.join('\n');

  return text.trim();
};

let textAreaDom: HTMLTextAreaElement | null = null;
const decodeHtmlEntities = (text: string) => {
  if (!textAreaDom) {
    textAreaDom = document.createElement('textarea');
  }
  textAreaDom.innerHTML = text;
  return textAreaDom.value;
};

export const convertChar = (text: string) => text.replace(/&nbsp;/gi, ' ');

export const getCleanTextExceptImg = (html: string) => {
  html = replaceEmoji2Str(html);

  const regP = /<\/p><p>/g;
  html = html.replace(regP, '</p><br><p>');

  const regBr = /<br\s*\/?>/gi;
  html = html.replace(regBr, '\n');

  const regWithoutHtmlExceptImg = /<(?!img\s*\/?)[^>]+>/gi;
  return html.replace(regWithoutHtmlExceptImg, '');
};

/**
 * 检测是否为表格相关行（表格行或分隔行）
 */
const isTableRelated = (line: string): boolean => {
  const trimmed = line.trim();
  // 表格行：包含 | 且不是空行
  // 表格分隔行：如 |---|---|
  return (
    (trimmed.includes('|') && trimmed !== '') ||
    /^\s*\|?[\s\-\|:]+\|?\s*$/.test(trimmed)
  );
};

/**
 * 检测是否为代码块标记
 */
const isCodeBlockMarker = (line: string): boolean => {
  return line.trim().startsWith('```');
};

/**
 * 处理 defaultValue 文本，为 Milkdown 编辑器优化换行符
 *
 * 功能：
 * 1. 识别 Markdown 表格和代码块
 * 2. 对于普通文本，在行与行之间添加空行以确保 Milkdown 正确渲染换行
 * 3. 保持表格和代码块的原始格式不变
 *
 * @param text 原始文本
 * @returns 处理后的文本
 */
export const processDefaultValueForEditor = (text: string): string => {
  if (!text) {
    return text;
  }

  const lines = text.split('\n');
  const result: string[] = [];
  let inCodeBlock = false;

  for (let i = 0; i < lines.length; i++) {
    const line = lines[i];
    const trimmedLine = line.trim();

    // 检测代码块开始/结束
    if (isCodeBlockMarker(line)) {
      inCodeBlock = !inCodeBlock;
      result.push(line);
      continue;
    }

    // 如果在代码块内，直接保留原样
    if (inCodeBlock) {
      result.push(line);
      continue;
    }

    result.push(line);

    // 对于普通文本行，如果下一行也是普通文本行，则添加空行
    if (trimmedLine !== '' && i < lines.length - 1) {
      const nextLine = lines[i + 1];
      const nextTrimmed = nextLine?.trim() || '';

      // 如果下一行不是空行、不是表格行、不是代码块标记，则添加空行
      if (
        nextTrimmed !== '' &&
        !isTableRelated(nextLine) &&
        !isCodeBlockMarker(nextLine) &&
        !isTableRelated(line)
      ) {
        result.push(''); // 添加空行
      }
    }
  }

  return result.join('\n');
};
