.sending {
  filter: opacity(0.7);
  transition: filter 1s;
}

.statusWrapper {
  width: max-content;
  max-width: 100%;
  display: flex;
  align-items: center;
  position: relative;

  .statusItem {
    // position: absolute;
    font-family: Microsoft Yahei;
    color: #636363;
    font-size: 13px;
    font-weight: 400;
    line-height: 30px;
  }

  .left {
    // right: -20px;
  }

  .right {
    // left: -20px;
    padding-right: 12px;
  }

  .sending,
  .failed {
    display: flex;
    align-items: center;
    flex-direction: row-reverse;

    img {
      height: 14px;
      width: 14px;
    }
  }

  .sending {
    /* 定义旋转的关键帧 */
    @keyframes rotate {
      from {
        transform: rotate(0deg);
      }

      to {
        transform: rotate(360deg);
      }
    }

    img {
      animation: rotate 2s linear infinite;

      /* 2秒完成一个周期，线性速度，无限次重复 */
    }
  }

  .failed {
    // cursor: pointer;
  }

  .Succeed {
    display: flex;
    align-items: center;
    flex-direction: row-reverse;
    // padding-left: 12px;
    font-size: 8px;
    font-weight: 600;
    color: #1e64aa;
    line-height: 11px;
    // position: absolute;
    // left: -20px;

    .allRead {
      width: 12px;
      height: 12px;
    }

    .notRead {
      width: 12px;
      height: 12px;
      border: 1px solid #1e64aa;
      border-radius: 100%;
    }

    .someRead {
      height: 12px;
      border: 1px solid #1e64aa;
      text-align: center;
      cursor: pointer;

      &.countLength1 {
        width: 12px;
        border-radius: 100%;
      }

      &.countLength2 {
        padding-left: 4px;
        padding-right: 4px;
        border-radius: 6px;
      }
    }
  }

  .historyFail {
    height: 14px;
    width: 14px;
    position: absolute;
    right: -24px;
  }
}

.messageReadListContainer {
  :global {
    .linkflow-popover-content {
      border-radius: 8px;
      border: 1px solid var(--primary-background-color-5);
    }

    .linkflow-popover-inner-content {
      padding: 0 !important;
      color: #fff !important;
      border-radius: 8px;
      border: 1px solid var(--primary-background-color-5);
    }

    .linkflow-popover-inner {
      background-color: transparent;
      box-shadow: none;
    }
  }
}
