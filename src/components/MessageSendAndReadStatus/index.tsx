import * as React from 'react';
import {
  MessageStatus,
  MessageItem,
  MessageType,
} from '@ht/openim-wasm-client-sdk';
import classNames from 'classnames';
import sendFailedIcon from '@/assets/coversation/sendFailedIcon.png';
import sendingIcon from '@/assets/coversation/sendingIcon.png';
import allReadIcon from '@/assets/channel/messageRender/allRead.png';
import { useSendMessage } from '@/hooks/useSendMessage';
import { useConversationStore, useUserStore } from '@/store';
import useThreadState from '@/hooks/useThreadState';
import { Popover } from '@ht/sprite-ui';
import { isBotUser } from '@/utils/avatar';
import { useMemo } from 'react';
import styles from './index.less';
import MessageReadList from '../MessageReadList';

const MessageSendAndReadStatus = ({
  children,
  message,
  conversationID,
  inRightThread,
  isSender = false,
  onMouseLeave,
  onMouseEnter,
  isSearch,
  inHistoryList = false,
  inMultiMessageModal = false,
  isExit = false,
}: {
  children: React.ReactNode;
  message: MessageItem;
  conversationID: string;
  inRightThread: boolean;
  isSender?: boolean;
  onMouseLeave: () => void;
  onMouseEnter: () => void;
  isSearch: boolean;
  inHistoryList: boolean;
  inMultiMessageModal: boolean;
  isExit: boolean;
}) => {
  const { selfInfo } = useUserStore();

  const isGroup = message.groupID != null && message.groupID !== '';

  const conversationList = useConversationStore(
    (state) => state.conversationList
  );
  const rightAreaInGroupConversation = useConversationStore(
    (state) => state.rightAreaInGroupConversation
  );
  const currentConversation = useConversationStore(
    (state) => state.currentConversation
  );

  const { createThread, joinThread } = useThreadState();

  const isInConversationWithBotOrSelf = useMemo(() => {
    if (isGroup) {
      return false;
    } else if (currentConversation?.userID == null) {
      return false;
    } else if (currentConversation?.userID === selfInfo.userID) {
      return true;
    } else {
      return isBotUser(currentConversation?.userID);
    }
  }, [currentConversation?.userID, isGroup, selfInfo.userID]);

  const conversation = conversationList.filter(
    (item) => item.conversationID === conversationID
  )?.[0];
  const { sendMessage } = useSendMessage(conversation);
  const getMessageStatusClassName = () => {
    if (message.status === MessageStatus.Sending) {
      return styles.sending;
    } else if (message.status === MessageStatus.Succeed) {
      return styles.sent;
    } else if (message.status === MessageStatus.Failed) {
      return styles.received;
    } else {
      return '';
    }
  };

  const renderMessageSendAndReadStatus = () => {
    // 先只放开文件类消息的发送状态，by zaa 0422
    if (
      message.status === MessageStatus.Sending &&
      message.contentType === MessageType.FileMessage
    ) {
      return (
        <div
          className={classNames(
            styles.statusItem,
            isSender ? styles.right : styles.left,
            styles.sending
          )}
          onMouseEnter={(e) => {
            e.stopPropagation();
            onMouseLeave();
          }}
          onMouseLeave={(e) => {
            e.stopPropagation();
            onMouseEnter();
          }}
        >
          <img src={sendingIcon}></img>
        </div>
      );
    } else if (
      // 发送中时也显示未读状态，避免等待时间过长
      (message.status === MessageStatus.Sending &&
        message.contentType !== MessageType.FileMessage) ||
      message.status === MessageStatus.Succeed
    ) {
      if (
        message.sendID !== selfInfo.userID ||
        isInConversationWithBotOrSelf ||
        isExit
      ) {
        // 只有自己发送的消息，以及非机器人、非与本人的对话中的、未退出的群的消息需要展示已读未读信息
        return;
      }
      const { hasReadCount } =
        message?.attachedInfoElem?.groupHasReadInfo || {};

      const { isRead } = message;

      // 全部已读
      if (isRead) {
        return (
          <div
            className={classNames(
              styles.Succeed,
              isSender ? styles.right : styles.left
            )}
            onMouseEnter={(e) => {
              e.stopPropagation();
              onMouseLeave();
            }}
            onMouseLeave={(e) => {
              e.stopPropagation();
              onMouseEnter();
            }}
          >
            <img src={allReadIcon} className={styles.allRead}></img>
          </div>
        );
      } else if (!isGroup && !isRead) {
        // 单聊未读
        return (
          <div
            className={classNames(
              styles.Succeed,
              isSender ? styles.right : styles.left
            )}
            onMouseEnter={(e) => {
              e.stopPropagation();
              onMouseLeave();
            }}
            onMouseLeave={(e) => {
              e.stopPropagation();
              onMouseEnter();
            }}
          >
            <div className={styles.notRead}></div>
          </div>
        );
      } else {
        // 群聊无人读、单聊未读、群聊没全读
        // const readIconPosition =
        //   hasReadCount?.toString().length === 1
        //     ? 20
        //     : 16 + 10 * hasReadCount?.toString().length;

        // const extraStyle = isSender ? 'left' : 'right';
        return (
          <Popover
            content={
              <MessageReadList
                conversationID={`sg_${message.groupID}`}
                clientMsgID={message.clientMsgID}
              />
            }
            destroyTooltipOnHide={true}
            trigger="click"
            overlayClassName={styles.messageReadListContainer}
            placement="left"
            showArrow={false}
            autoAdjustOverflow={false}
          >
            <div
              className={classNames(
                styles.Succeed,
                isSender ? styles.right : styles.left
              )}
            >
              <div
                className={classNames(
                  styles.someRead,
                  hasReadCount?.toString().length === 1
                    ? styles.countLength1
                    : styles.countLength2
                )}
                style={{ cursor: 'pointer' }}
                onMouseEnter={(e) => {
                  e.stopPropagation();
                  onMouseLeave();
                }}
                onMouseLeave={(e) => {
                  e.stopPropagation();
                  onMouseEnter();
                }}
              >
                {hasReadCount !== 0 ? hasReadCount : ''}
              </div>
            </div>
          </Popover>
        );
      }
    } else if (message.status === MessageStatus.Failed) {
      return (
        <div
          className={classNames(
            styles.statusItem,
            styles.failed,
            isSender ? styles.right : styles.left
          )}
          onMouseEnter={(e) => {
            e.stopPropagation();
            onMouseLeave();
          }}
          onMouseLeave={(e) => {
            e.stopPropagation();
            onMouseEnter();
          }}
        >
          <img
            src={sendFailedIcon}
            style={{
              cursor:
                message.contentType === MessageType.TextMessage
                  ? 'pointer'
                  : '',
            }}
            // 此段代码其实在MessageInput组件中冗余出现，为了不影响，暂时先原样搬过来
            onClick={async () => {
              // 暂时先支持文本的重试
              if (message.contentType !== MessageType.TextMessage) {
                return;
              }

              if (rightAreaInGroupConversation === 'thread') {
                // 在已创建的thread中打开，点击发送
                if (conversation?.groupID != null) {
                  const joinThreadResult = await joinThread(
                    conversation?.groupID
                  );
                  if (joinThreadResult) {
                    await sendMessage({ message });
                  }
                } // 首次打开，需先创建thread
                else {
                  const threadID = await createThread();

                  if (threadID != null) {
                    await sendMessage({
                      message,
                      groupID: threadID,
                      inRightThread,
                    });
                  } else {
                    console.error('创建thread失败，原因是threadID');
                  }
                }
              } else {
                sendMessage({ message });
              }
            }}
          ></img>
        </div>
      );
    } else {
      return '';
    }
  };

  const calcMaxWidth = useMemo(() => {
    return inHistoryList || inMultiMessageModal ? '100%' : 'calc(100% - 47px)';
  }, [inHistoryList, inMultiMessageModal]);

  return (
    <div
      className={classNames(getMessageStatusClassName(), styles.statusWrapper)}
      style={isSender ? { float: 'right' } : { maxWidth: calcMaxWidth }}
    >
      {isSender && (
        <div style={{ width: '47px' }}>
          {!isSearch && renderMessageSendAndReadStatus()}
        </div>
      )}
      <div
        style={
          isSender ? { maxWidth: 'calc(100% - 47px)' } : { maxWidth: '100%' }
        }
      >
        {children}
      </div>
      {inHistoryList && message.status === MessageStatus.Failed ? (
        <img src={sendFailedIcon} className={styles.historyFail} />
      ) : (
        ''
      )}
    </div>
  );
};

export default MessageSendAndReadStatus;
