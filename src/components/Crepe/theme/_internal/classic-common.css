.milkdown {
  position: relative;
  height: 100%;
  padding: 0;
  font-weight: 400;
  font-family: var(--crepe-font-default);
  background-color: transparent;
  color: var(--crepe-color-on-background);

  * {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
  }

  /* button, */
  input {
    border: none;
    background: none;
    box-shadow: none;
    &:focus {
      outline: none;
    }
  }

  :focus-visible {
    outline: none;
  }

  /* background: var(--crepe-color-background); */

  .ProseMirror-focused {
    outline: none;
  }

  .ProseMirror {
    height: 100%;
    padding: 0;

    /* font-family: Microsoft YaHei, EmojiMart; */
    font-family: Microsoft Yahei, "lato", EmojiMart;

    &.editor {
      overflow-y: auto;
    }

    *::selection {
      background: var(--crepe-color-selected);
    }

    li.ProseMirror-selectednode {
      background: var(--crepe-color-selected);
      outline: none;
      ::selection {
        background: transparent;
      }
      &::selection {
        background: transparent;
      }
    }
    li.ProseMirror-selectednode::after {
      all: unset;
    }

    .ProseMirror-selectednode {
      outline: none;

      /* background: color-mix(
        in srgb,
        var(--crepe-color-selected),
        transparent 60%
      ); */
      ::selection {
        background: transparent;
      }
      &::selection {
        background: transparent;
      }
    }

    &[data-dragging="true"] {
      .ProseMirror-selectednode,
      &::selection,
      *::selection {
        background: transparent;
      }

      input::selection {
        background: var(--crepe-color-selected);
      }
    }

    img {
      vertical-align: bottom;
      max-width: 100%;

      &.ProseMirror-selectednode {
        background: none;
        outline: 2px solid var(--crepe-color-primary);
      }
    }

    h1,
    h2,
    h3,
    h4,
    h5,
    h6 {
      /* font-family: var(--crepe-font-title); */
      padding: 8px 0;
      color: #000000;
      font-weight: 600;
    }

    h1 {
      font-size: 24px;
      line-height: 36px;
    }

    h2 {
      font-size: 20px;
      line-height: 32px;
    }

    h3 {
      font-size: 18px;
      line-height: 26px;
    }

    h4 {
      font-size: 16px;
      line-height: 24px;
    }

    h5 {
      font-size: 15px;
      line-height: 20px;
    }

    h6 {
      font-size: 15px;
      line-height: 20px;
    }

    p {
      font-size: 15px;
      line-height: 22px;
      white-space: normal;
      word-wrap: break-word;
      overflow-wrap: break-word;
      word-break: break-word;
      padding: 0;
      color: var(--primary-text-color-1);
    }
    strong {
      font-family: Microsoft Yahei, "lato", EmojiMart;
    }

    code {
      color: var(--crepe-color-inline-code);
      background: color-mix(
        in srgb,
        var(--crepe-color-inline-area),
        transparent 40%
      );
      font-family: var(--crepe-font-code);
      padding: 0 10px;
      border-radius: 4px;
      font-size: 87.5%;
      display: inline-block;
      line-height: 1.4286;
    }

    a {
      color: var(--crepe-color-primary);
      text-decoration: underline;
    }

    pre {
      background: color-mix(
        in srgb,
        var(--crepe-color-inline-area),
        transparent 40%
      );

      /* padding: 10px; */
      border-radius: 4px;
      code {
        padding: 0;
        background: transparent;
      }
    }

    blockquote {
      position: relative;
      padding-left: 15px;
      padding-top: 0;
      padding-bottom: 0;
      box-sizing: content-box;

      &::before {
        content: "";
        width: 4px;
        left: 0;
        top: 4px;
        bottom: 4px;
        position: absolute;
        background: var(--crepe-color-selected);
        border-radius: 100px;
      }

      hr {
        padding-bottom: 16px;
      }
    }

    hr {
      border: none;
      background-color: color-mix(
        in srgb,
        var(--crepe-color-outline),
        transparent 80%
      );
      background-clip: content-box;
      padding: 6px 0;
      height: 13px;
      position: relative;

      &.ProseMirror-selectednode {
        outline: none;
        background-color: color-mix(
          in srgb,
          var(--crepe-color-outline),
          transparent 20%
        );
        background-clip: content-box;
        &::before {
          content: "";
          position: absolute;
          left: 0;
          top: 0;
          bottom: 0;
          right: 0;
          background-color: color-mix(
            in srgb,
            var(--crepe-color-outline),
            transparent 80%
          );
          pointer-events: none;
        }
      }
    }

    ul,
    ol {
      padding: 0;
    }
  }
}
