/* eslint-disable eslint-comments/disable-enable-pair */
/* eslint-disable react/no-unused-prop-types */
/*
 * @Author: ypt
 * @Description:
 * @LastEditors: ypt
 * @LastEditTime: 2025-01-23 15:53:27
 * @FilePath: /linkflow/src/components/Channel/components/MessageItem/index.tsx
 */
import { MessageType } from '@ht/openim-wasm-client-sdk';
import { FC, memo } from 'react';
import TextMessageRender from '@/components/Channel/components/MessageItem/TextMessageRender';
import CustomMessageRender from '@/components/Channel/components/MessageItem/CustomMessageRender';
import PictureMessageRender from '@/components/Channel/components/MessageItem/PictureMessageRender';
import FaceMessageRender from '@/components/Channel/components/MessageItem/FaceMessageRender';
import FileMessageRender from '@/components/Channel/components/MessageItem/FileMessageRender';
import MergeMessageRender from '@/components/Channel/components/MessageItem/MergeMessageRender';
import AtTextMessageRender from '@/components/Channel/components/MessageItem/AtTextMessageRender';
import QuoteMessageRender from '@/components/Channel/components/MessageItem/QuoteMessageRender';
import GroupAnnouncementRender from '@/components/Channel/components/MessageItem/GroupAnnouncementRender';
import { IMessageItemProps } from '@/components/Channel/components/MessageItem/index';

const components: Record<number, FC<IMessageItemProps>> = {
  [MessageType.TextMessage]: TextMessageRender,
  [MessageType.CustomMessage]: CustomMessageRender,
  [MessageType.PictureMessage]: PictureMessageRender,
  [MessageType.FaceMessage]: FaceMessageRender,
  [MessageType.FileMessage]: FileMessageRender,
  [MessageType.AtTextMessage]: AtTextMessageRender,
  [MessageType.MergeMessage]: MergeMessageRender,
  [MessageType.QuoteMessage]: QuoteMessageRender,
  [MessageType.GroupAnnouncementUpdated]: GroupAnnouncementRender,
};

const MultiMessageItem: FC<IMessageItemProps> = ({
  message,
  disabled = false,
  isSender,
  conversationID,
  showName,
  isThread,
  inRightThread,
  scrollToBottomSmooth,
  isForwardMessage = false,
  isQuoteMessage = false,
  picStyle = {},
  isExit = false,
  hasScrolled = false,
  isSearch = false,
  messageIndex,
  virtuoso,
  hasMoreMessageBefore,
  inHistoryList = false,
  inMultiMessageModal = false,
  historyTab = '',
  searchValue = '',
  markConversationMessageAsRead,
  isLastMsg,
  prevMsg,
  hasForwardBtn = false,
}) => {
  const MessageRenderComponent =
    components[message.contentType] || TextMessageRender;

  return (
    <div>
      <div>
        <MessageRenderComponent
          key={message.clientMsgID}
          isThread={isThread}
          inRightThread={inRightThread}
          message={message}
          isSender={isSender}
          disabled={disabled}
          conversationID={conversationID}
          showName={showName}
          scrollToBottomSmooth={scrollToBottomSmooth}
          isForwardMessage={isForwardMessage}
          isQuoteMessage={isQuoteMessage}
          picStyle={picStyle}
          isExit={isExit}
          clientMsgId={message.clientMsgID}
          sendID={message.sendID}
          hasScrolled={hasScrolled}
          isSearch={isSearch}
          messageIndex={messageIndex}
          virtuoso={virtuoso}
          hasMoreMessageBefore={hasMoreMessageBefore}
          inHistoryList={inHistoryList}
          inMultiMessageModal={inMultiMessageModal}
          historyTab={historyTab}
          searchValue={searchValue}
          markConversationMessageAsRead={markConversationMessageAsRead}
          isLastMsg={isLastMsg}
          prevMsg={prevMsg}
          hasForwardBtn={hasForwardBtn}
        />
      </div>
    </div>
  );
};

export default memo(MultiMessageItem);
