.messageUpContentWrapper {
  border: 1px solid #dddee0;
  background: #ffffff;
  border-radius: 8px;
  padding: 10px 12px;
  display: flex;
  justify-content: space-between;

  .line {
    width: 4px;
    background: #0074e2;
    border-radius: 4px;
    margin-right: 9px;
  }

  .contentWrapper {
    flex: 1;
    overflow: hidden;

    .text {
      font-size: 15px;
      color: #000000;
      line-height: 18px;
      display: flex;
      white-space: nowrap;
      font-family: Microsoft Yahei, "lato", EmojiMart;
    }

    .upDesc {
      font-size: 12px;
      font-weight: 400;
      color: #666771;
      line-height: 16px;
      display: flex;
      align-items: center;

      .upBy {
        color: #0074e2;
        margin-right: 12px;
      }
    }

    .descWrapper {
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      overflow: hidden;
      height: 38px;
    }

    .fileRender {
      display: flex;

      .fileIcon {
        width: 38px;
        height: 38px;
        margin-right: 9px;

        > img {
          width: 38px;
          height: 38px;
        }
      }
    }

    .pictureRender {
      display: flex;
      .pictureIcon {
        width: 38px;
        height: 38px;
        margin-right: 9px;
        border-radius: 4px;
        overflow: hidden;
        > img {
          width: 38px;
          height: 38px;
        }
      }
    }
  }

  .revokeWarp {
    line-height: 28px;
  }

  .closeIcon {
    width: 28px;
    height: 28px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: 30px;
    align-self: center;

    &:hover {
      background: rgba(107, 107, 108, 8%);
      border-radius: 6px;
      cursor: pointer;
    }
  }

  .confirmText {
    font-size: 15px;
    font-weight: 400;
    color: #1d1c1d;
    line-height: 22px;
  }
}

.msgUpClearTooltip {
  :global {
    .linkflow-tooltip-inner {
      padding: 7px 16px;
      border-radius: 8px;
      box-shadow: 0 6px 16px 0 rgba(29, 34, 44, 8%),
        0 3px 6px -4px rgba(29, 34, 44, 12%);
      font-size: 14px;
      font-weight: 400;
      color: #ffffff;
      line-height: 20px;
    }
    .linkflow-tooltip-arrow {
      bottom: 1px;
    }
  }
}
