/* eslint-disable eslint-comments/disable-enable-pair */
/* eslint-disable max-lines */
/* eslint-disable indent */
import { FC, useEffect, useState, useMemo } from 'react';
import classNames from 'classnames';
import _ from 'lodash';
import { Popover, Tooltip, Upload, Button } from '@ht/sprite-ui';
import { RightOutlined } from '@ht-icons/sprite-ui-react';
import fileIcon from '@/assets/channel/messageInput/file.svg';
import showFormaterIcon from '@/assets/channel/messageInput/showFormater.svg';
import hideFormaterIcon from '@/assets/channel/messageInput/hideFormater.svg';
import emojiIcon from '@/assets/channel/messageInput/emoji.svg';
import atIcon from '@/assets/channel/messageInput/at.svg';
import audioIcon from '@/assets/channel/messageInput/audio.png';
import screenshotIcon from '@/assets/channel/messageInput/screenshot2.svg';
import arrowIcon from '@/assets/channel/messageInput/arrow.svg';
import videoIcon from '@/assets/channel/messageInput/video.png';
import cloudDocIcon from '@/assets/channel/messageInput/cloudDoc.png';
import localUploadIcon from '@/assets/channel/messageInput/localUpload.png';
import imageUpload from '@/assets/channel/messageInput/imageUpload.svg';
import localUploadActiveIcon from '@/assets/channel/messageInput/localUploadActive.png';
import CloudDocSelect from '@/components/CloudDocSelect';
import EmojiComponentNew from './EmojiComponentNew';
import styles from './index.less';

declare global {
  interface Window {
    __TAURI_INTERNALS__: any;
  }
}
// const uploadArr = ['video', 'audio'];

const switchType = (type: string) => {
  switch (type) {
    case 'audio':
      return 'audio/*';
    case 'video':
      return 'video/*';
    case 'file':
      return '*';
    case 'image':
      return 'image/*';
    default:
      return '*';
  }
};

interface InputFooterRenderProps {
  showFormater: boolean;
  changShowFormater: (val: boolean) => void;
  customRequest?: (data: any, type: string, path?: string) => void;
  insertEmoji: (val: string) => void;
  pasteContainerRef: React.RefObject<any>;
  conversationID?: string;
  cloudUpload?: (data: any) => void;
  disabledBtn?: boolean;
  isGroup?: boolean;
  startRecord: () => void;
  endRecord: () => void;
  isMultiModalBotConv?: boolean; // 添加多轮会话标志
}

type allExtendOperateListProps = {
  idx?: number;
  title?: string;
  icon?: string;
  type: string;
  show: boolean;
};

const InputFooterRender: FC<InputFooterRenderProps> = ({
  showFormater = true,
  changShowFormater,
  customRequest,
  insertEmoji,
  pasteContainerRef,
  conversationID,
  cloudUpload,
  disabledBtn = false,
  isGroup = true,
  startRecord,
  endRecord,
  isMultiModalBotConv = false,
}) => {
  const browserWindow = window?.htElectronSDK?.BrowserWindow;
  const [fileOpen, setFileOpen] = useState<boolean>(false);
  const [screenshotOpen, setScreenshotOpen] = useState<boolean>(false);
  const [open, setOpen] = useState<boolean>(false);
  const [cloudOpen, setCloudOpen] = useState<boolean>(false);
  const [recordBtnMsg, setRecordBtnMsg] = useState<string>('按住说话');
  const allExtendOperateList: allExtendOperateListProps[] = useMemo(() => {
    return [
      {
        idx: 0,
        title: '表情',
        icon: emojiIcon,
        type: 'emoji',
        show: !isMultiModalBotConv,
      },
      {
        idx: 1,
        title: '截图shift+alt+A',
        icon: screenshotIcon,
        type: 'screenshot',
        show: !_.isEmpty(window.electronAPI) || false,
      },
      {
        idx: 2,
        title: '图片',
        icon: imageUpload,
        type: 'image',
        show: true,
      },
      {
        idx: 3,
        title: '文件',
        icon: fileIcon,
        type: 'file',
        show: true,
      },
      {
        idx: 4,
        title: '提及某个成员',
        icon: atIcon,
        type: 'at',
        show: isGroup,
      },
      {
        idx: 5,
        title: showFormater ? '隐藏格式' : '显示格式',
        icon: showFormater ? showFormaterIcon : hideFormaterIcon,
        type: 'showOrHiddenFormater',
        show: true,
      },
      // {
      //   idx: 6,
      //   title: '音频',
      //   icon: audioIcon,
      //   type: 'audio',
      // },
      {
        idx: 6,
        title: '语音录入',
        icon: audioIcon,
        type: 'record',
        // show: location.href.includes('linkflow/singleChat'),
        show: false,
      },
    ];
  }, [isMultiModalBotConv, isGroup, showFormater]);

  const handleScreenshot = async (hideWindow: boolean) => {
    browserWindow?.onCaptureClick(hideWindow);
  };

  const extendOperateClick = async (type: string) => {
    if (disabledBtn) {
      return;
    }
    switch (type) {
      case 'showOrHiddenFormater':
        localStorage.setItem(
          'linkim.messageInput.formatterDisplayPref',
          JSON.stringify(!showFormater)
        );

        changShowFormater(!showFormater);
        break;
      case 'at':
        insertAt();
        break;
      case 'screenshot':
        handleScreenshot(false);
        break;
      default:
    }
  };

  const insertAt = () => {
    insertEmoji('@');
  };

  const electronUpload = async (type: string) => {
    // 多轮会话场景下支持多文件选择
    const properties = isMultiModalBotConv
      ? ['openFile', 'multiSelections']
      : ['openFile'];
    const filters =
      type === 'image'
        ? [{ name: 'Images', extensions: ['jpg', 'jpeg', 'png', 'gif', 'bmp'] }]
        : [];

    window?.electronAPI
      ?.ipcInvoke('open-file-dialog', properties, filters)
      .then((result: any) => {
        console.warn('result', result);
        const { isSuccess = false, file, files } = result || {};
        if (isSuccess) {
          if (isMultiModalBotConv && files && files.length) {
            // 处理多文件上传
            files.forEach((fileItem: any) => {
              customRequest?.({ file: fileItem }, type);
            });
          } else {
            // 处理单文件上传
            customRequest?.({ file }, type);
          }
          setFileOpen(false);
        }
      });
  };

  const onCaptureHideCurrentWindow = async () => {
    handleScreenshot(true);
  };

  return (
    <div className={styles.extendOperate}>
      {allExtendOperateList.map((item: any, index: number) => {
        if (item.type && item.type === 'divider') {
          return (
            // eslint-disable-next-line react/no-array-index-key
            <div key={`divider-${index}`} className={styles.line}></div>
          );
        } else if (item.type && item.type === 'emoji' && item.show) {
          return (
            <EmojiComponentNew
              key={item.idx}
              handleEmojiSelect={(emoji: any) => {
                insertEmoji(emoji);
              }}
              disabledBtn={disabledBtn}
              item={item}
            />
          );
        } else if (item.type === 'file' && item.show) {
          return (
            <Tooltip
              key={item.idx}
              title={item.title}
              overlayClassName={styles.tooltipWrap}
              trigger={'hover'}
              onOpenChange={(val: boolean) => {
                if (!fileOpen) {
                  setOpen(val);
                }
              }}
              open={open}
              overlayStyle={disabledBtn ? { display: 'none' } : {}}
            >
              <div
                key={item.idx}
                className={classNames(
                  styles.operateItem,
                  disabledBtn && styles.operateItemdisabled,
                  item.type === 'file' && styles.operateFileItem
                )}
              >
                <>
                  {window.electronAPI ? (
                    <img
                      src={item.icon}
                      alt={item.title}
                      onClick={(e) => {
                        e.stopPropagation();
                        electronUpload(item.type);
                      }}
                    />
                  ) : (
                    <Upload
                      accept={switchType(item.type)}
                      action={''}
                      customRequest={(data) => {
                        customRequest?.(data, item.type);
                        setFileOpen(false);
                      }}
                      showUploadList={false}
                      multiple={false}
                    >
                      <img
                        src={item.icon}
                        alt={item.title}
                        className={styles.imgLeft}
                        onClick={() => setOpen(false)}
                      />
                    </Upload>
                  )}

                  {!isMultiModalBotConv && (
                    <Popover
                      open={fileOpen}
                      onOpenChange={(val: boolean) => {
                        if (disabledBtn) {
                          return;
                        }
                        if (val) {
                          setOpen(false);
                        }
                        setFileOpen(val);
                      }}
                      trigger={'click'}
                      placement="topLeft"
                      overlayClassName={styles.filePopoverWarp}
                      content={
                        <div className={styles.filePopoverContent}>
                          <Popover
                            trigger={'hover'}
                            placement="right"
                            open={cloudOpen}
                            content={
                              <CloudDocSelect
                                conversationID={conversationID!}
                                onChange={(val: any) => {
                                  setCloudOpen(false);
                                  cloudUpload?.(val);
                                  setFileOpen(false);
                                }}
                              />
                            }
                            destroyTooltipOnHide={true}
                            onOpenChange={(val: boolean) => setCloudOpen(val)}
                            overlayClassName={styles.cloudDocSelectPopoverWarp}
                          >
                            <div className={styles.item}>
                              <div className={styles.desc}>
                                <img
                                  src={cloudDocIcon}
                                  className={styles.show}
                                />
                                <span>发送云文档</span>
                              </div>
                              <RightOutlined />
                            </div>
                          </Popover>
                          {window.electronAPI ? (
                            <div
                              className={styles.item}
                              onClick={() => {
                                electronUpload(item.type);
                              }}
                            >
                              <div className={styles.desc}>
                                <img
                                  src={localUploadIcon}
                                  className={styles.show}
                                />
                                <span>发送本地文件</span>
                              </div>
                            </div>
                          ) : (
                            <Upload
                              accept={switchType(item.type)}
                              key={item.idx}
                              action={''}
                              customRequest={(data) => {
                                customRequest?.(data, item.type);
                                setFileOpen(false);
                              }}
                              showUploadList={false}
                              multiple={false}
                            >
                              <div className={styles.item}>
                                <div className={styles.desc}>
                                  <img
                                    src={localUploadIcon}
                                    className={styles.show}
                                  />
                                  <span>发送本地文件</span>
                                </div>
                              </div>
                            </Upload>
                          )}
                        </div>
                      }
                    >
                      <img src={arrowIcon} className={styles.arrowIcon} />
                    </Popover>
                  )}
                </>
              </div>
            </Tooltip>
          );
        } else if (item.type === 'image' && item.show) {
          return (
            <Tooltip
              key={item.idx}
              title={item.title}
              overlayClassName={styles.tooltipWrap}
              trigger={'hover'}
              overlayStyle={disabledBtn ? { display: 'none' } : {}}
            >
              <div
                key={item.idx}
                className={classNames(
                  styles.operateItem,
                  disabledBtn && styles.operateItemdisabled
                )}
              >
                <>
                  {window.electronAPI ? (
                    <img
                      src={item.icon}
                      alt={item.title}
                      onClick={(e) => {
                        e.stopPropagation();
                        electronUpload(item.type);
                      }}
                    />
                  ) : (
                    <Upload
                      accept={switchType(item.type)}
                      action={''}
                      customRequest={(data) => {
                        customRequest?.(data, item.type);
                      }}
                      showUploadList={false}
                    >
                      <img
                        src={item.icon}
                        alt={item.title}
                        className={styles.imgLeft}
                      />
                    </Upload>
                  )}
                </>
              </div>
            </Tooltip>
          );
        } else if (item.type === 'screenshot' && item.show) {
          return (
            <div
              key={item.idx}
              onMouseDown={(e) => e.preventDefault()}
              className={classNames(
                styles.operateItem,
                disabledBtn && styles.operateItemdisabled
              )}
            >
              <Tooltip
                title={item.title ? item.title : null}
                overlayClassName={styles.tooltipWrap}
                overlayStyle={disabledBtn ? { display: 'none' } : {}}
              >
                <>
                  <img
                    src={item.icon}
                    alt={item.title}
                    className={styles.imgLeft}
                    onClick={() => extendOperateClick(item.type)}
                  />
                  <Popover
                    trigger={'click'}
                    placement="bottomLeft"
                    overlayClassName={styles.filePopoverWarp}
                    open={screenshotOpen}
                    onOpenChange={(val: boolean) => {
                      setScreenshotOpen(val);
                    }}
                    content={
                      <div
                        className={styles.filePopoverContent}
                        onClick={(e) => {
                          e.stopPropagation();
                          onCaptureHideCurrentWindow();
                          setScreenshotOpen(false);
                        }}
                      >
                        <div className={styles.item}>
                          <div className={styles.desc}>
                            <span>隐藏当前窗口截图</span>
                          </div>
                        </div>
                      </div>
                    }
                  >
                    <img src={arrowIcon} className={styles.arrowIcon} />
                  </Popover>
                </>
              </Tooltip>
            </div>
          );
        } else if (item.type === 'record' && item.show) {
          return (
            <Button
              key={item.idx}
              onMouseDown={() => {
                setRecordBtnMsg('正在录音');
                startRecord();
              }}
              onMouseUp={() => {
                setRecordBtnMsg('按住录音');
                endRecord();
              }}
            >
              {recordBtnMsg}
            </Button>
          );
        } else if (item.show) {
          return (
            <div
              key={item.idx}
              onClick={() => extendOperateClick(item.type)}
              onMouseDown={(e) => e.preventDefault()}
              className={classNames(
                styles.operateItem,
                disabledBtn && styles.operateItemdisabled
              )}
            >
              <Tooltip
                title={item.title ? item.title : null}
                overlayClassName={styles.tooltipWrap}
                overlayStyle={disabledBtn ? { display: 'none' } : {}}
              >
                <img src={item.icon} alt={item.title} />
              </Tooltip>
            </div>
          );
        }
      })}
    </div>
  );
};
export default InputFooterRender;
