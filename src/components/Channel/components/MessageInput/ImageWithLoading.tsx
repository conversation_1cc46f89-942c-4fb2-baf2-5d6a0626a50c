import { FC, useEffect, useState, useCallback } from 'react';

interface ImageWithLoadingProps
  extends React.ImgHTMLAttributes<HTMLImageElement> {
  src: string;
  hoverSrc?: string;
  loadingSrc: string;
}

const ImageWithLoading: FC<ImageWithLoadingProps> = ({
  src,
  hoverSrc,
  loadingSrc,
  ...rest
}) => {
  const [loadedMap, setLoadedMap] = useState<Record<string, boolean>>({});
  const [currentSrc, setCurrentSrc] = useState(src);

  // 预加载图片
  const preload = useCallback(
    (url: string) => {
      if (!url || loadedMap[url]) {
        return;
      } // 已加载过就跳过
      const img = new Image();
      img.src = url;
      img.onload = () => setLoadedMap((prev) => ({ ...prev, [url]: true }));
      img.onerror = () => setLoadedMap((prev) => ({ ...prev, [url]: true }));
    },
    [loadedMap]
  );

  // 初始化预加载
  useEffect(() => {
    preload(src);
    if (hoverSrc) {
      preload(hoverSrc);
    }
  }, [src, hoverSrc, preload]);

  // src 变化时切回主图
  useEffect(() => {
    setCurrentSrc(src);
  }, [src]);

  return (
    <img
      src={loadedMap[currentSrc] ? currentSrc : loadingSrc}
      onMouseEnter={() => {
        if (hoverSrc) {
          setCurrentSrc(hoverSrc);
        }
      }}
      onMouseLeave={() => {
        if (hoverSrc) {
          setCurrentSrc(src);
        }
      }}
      {...rest}
    />
  );
};

export default ImageWithLoading;
