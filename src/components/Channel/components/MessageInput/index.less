.content {
  width: 100%;
  height: 100%;
  max-height: 60vh;
  border-radius: 8px;
  // border: 1px solid var(--offline-border-color);
  display: flex;
  flex-direction: column;
  // padding: 2px;
  position: relative;
  // overflow-x: hidden;
  background: var(--primary-background-color-6);
  box-shadow: 0 4px 16px 0 rgba(0, 0, 0, 10%);

  .robotConfigWarp {
    width: 100%;
    padding-top: 6px;
    background: var(--primary-background-color-17);
    position: absolute;
    top: -40px;
  }
  .robotConfig {
    width: 110px;
    height: 28px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--primary-background-color-6);
    box-shadow: 0 2px 10px 0 var(--primary-background-color-13);
    border-radius: 8px;
    font-size: 12px;
    font-weight: 400;
    color: var(--primary-text-color-8);
    line-height: 20px;
    cursor: pointer;
    > img {
      width: 14px;
      height: 14px;
      margin-right: 4px;
    }
  }

  .placeholder {
    padding-top: 2px;
    max-width: calc(100% - 24px);
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    color: #cacaca;
    position: absolute;
    // top: 12px;
    left: 13px;
    .state {
      img {
        position: relative;
        top: -2px;
      }
    }
  }

  .textWarp {
    font-family: Microsoft Yahei, "lato", EmojiMart;
    // max-height: calc(100% - 40px);
    // min-height: 86px;
    // height: 200px;
    // margin-bottom: 8px;
    // overflow-y: scroll;
    flex: 1 1;
    min-height: 0; // min-height：auto会导致flex收缩失效
    // textarea {
    //   height: 100%;
    //   border: none;
    // }
  }
  .footer {
    height: 40px;
    display: flex;
    justify-content: space-between;
    // margin: 0;
    .sendBtn {
      width: 60px;
      height: 28px;
      border-radius: 6px;
      background-color: #bbbabb;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-top: 6px;
      margin-right: 6px;
      cursor: pointer;
    }
    .canSend {
      background: #0074e2;
      &:hover {
        background: #0084ff;
      }
    }
    .cannotSend {
      cursor: auto;
    }
  }
  .box {
    width: 100%;
    height: 100%;
    position: absolute;
    border-radius: 8px;
    background-color: var(--primary-background-color-14);
    top: 0;
    left: 0;
  }
}

.replyBox {
  max-width: calc(100% - 40px);
  width: max-content;
  // background: var(--msg-qute-backgroud-color);
  border-radius: 4px;
  margin: 8px 8px 0;
  // padding: 8px 16px;
  display: flex;
  font-size: 12px;
  font-weight: 500;
  color: var(--primary-text-color);
  position: relative;
  > div {
    max-width: 100%;
  }
  .removeReplyMsg {
    width: 22px;
    height: 22px;
    position: absolute;
    right: -11px;
    top: -11px;
    display: none;
    cursor: pointer;
    > img {
      width: 100%;
      height: 100%;
    }
  }
}
.replyBox:hover {
  .removeReplyMsg {
    display: block;
  }
}
.memberKickedMsg {
  display: flex;
  justify-content: center;
  width: 100%;
  color: var(--primary-text-color-3);
  font-size: 14px;
  font-weight: 400;
  line-height: 20px;
  position: absolute;
  top: calc(50% - 10px);
  z-index: 1;
}

@keyframes progressAnimation {
  0% {
    transform: translateX(0%);
  }
  100% {
    transform: translateX(99%);
  }
}

.fileRenderWarp {
  width: 100%;
  display: flex;
  position: relative;
  .fileRenderContent {
    display: flex;
    padding: 8px 8px 0;
    width: 100%;
    overflow-y: auto;
    &::-webkit-scrollbar {
      height: 0;
    }
  }
  .fileItem,
  .cloudDocItm,
  .fileImgItem {
    flex-shrink: 0;
    display: flex;
    border-radius: 10px;
    border: 1px solid var(--offline-border-color);
    align-items: center;
    margin-right: 12px;
    position: relative;
    .icon {
      flex-shrink: 0;
      width: 41px;
      height: 41px;
      border-radius: 8px;
      overflow: hidden;
      margin-right: 12px;
    }
    .fileInfo {
      flex: 1 1;
      display: flex;
      // align-items: center;
      flex-direction: column;
      font-size: 12px;
      font-weight: 400;
      color: var(--primary-text-color);
      line-height: 17px;
      overflow: hidden;
      .fileName {
        width: 100%;
        font-size: 16px;
        color: var(--primary-text-color-1);
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        margin-bottom: 2px;
      }
      .info {
        height: 17px;
      }
    }
    .closeIocn {
      display: none;
      width: 16px;
      height: 16px;
      cursor: pointer;
      position: absolute;
      right: -8px;
      top: -8px;
    }
  }
  .fileItem {
    width: 234px;
    height: 62px;
    // background: #ffffff;
    padding: 0 12px;
    position: relative;

    &.fileItemFailed {
      background-color: #fff1f0;
    }

    .progressBar {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-color: transparent;
      z-index: 0;
      overflow: hidden;

      .progress {
        position: absolute;
        top: 0;
        left: -100%;
        height: 100%;
        width: 100%;
        background-color: var(--primary-background-color-5);
        opacity: 0.5;
        animation: progressAnimation 3s linear forwards;
      }
    }

    .icon {
      position: relative;
      z-index: 1;
      display: flex;
      align-items: center;
      justify-content: center;
      > img {
        width: 40px;
        height: 40px;
      }

      &.iconFaded {
        opacity: 0.5;
      }
    }

    .fileInfo {
      position: relative;
      z-index: 1;

      .fileName {
        &.textFaded {
          opacity: 0.5;
        }
      }

      .info {
        .failedInfo {
          display: flex;
          align-items: center;

          .failedText {
            color: #ff4d4f;
          }

          .retryText {
            color: #ff4d4f;
            cursor: pointer;
            text-decoration: underline;
          }
        }
      }
    }
  }
  .fileImgItem {
    width: 65px;
    height: 65px;
    img {
      width: 100%;
      height: 100%;
      object-fit: contain;
    }

    > div {
      display: flex;
      width: 100%;
      height: 100%;
      align-items: center;
      justify-content: center;
      overflow: hidden;
      border-radius: 10px;
      position: relative;

      .imgProgressBar {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: transparent;
        border-radius: 10px;
        z-index: 1;
        overflow: hidden;

        .imgProgress {
          position: absolute;
          top: 0;
          left: -100%;
          height: 100%;
          width: 100%;
          background-color: var(--primary-background-color-5);
          opacity: 0.5;
          border-radius: 10px;
          animation: progressAnimation 3s linear forwards;
        }
      }

      .uploadStatus {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 10px;

        &.failed {
          background: rgba(255, 77, 79, 80%);
          cursor: pointer;

          .failedText {
            color: #ffffff;
            font-size: 10px;
            font-weight: 500;
          }
        }

        &.success {
          .successText {
            color: #ffffff;
            font-size: 16px;
            font-weight: bold;
          }
        }
      }
    }
  }

  .fileImgItem:hover {
    .closeIocn {
      display: flex;
    }
  }
  .fileItem:hover {
    .closeIocn {
      display: flex;
    }
  }
  .fileItem:last-child {
    margin-right: 0;
  }
  .cloudDocItm {
    width: 340px;
    height: 65px;
    .icon {
      display: flex;
      align-items: center;
      justify-content: center;
      margin-left: 12px;
      > img {
        width: 40px;
        height: 40px;
      }
    }
    .fileInfo {
      .info {
        display: flex;
        .line {
          width: 1px;
          height: 14px;
          background: var(--primary-background-color-5);
          margin: 0 4px;
        }
        > span {
          white-space: nowrap;
        }
      }
    }
  }
  .cloudDocItm:hover {
    .closeIocn {
      display: flex;
    }
  }
  .leftArrow,
  .rightArrow {
    width: 45px;
    height: 45px;
    position: absolute;
    top: 20px;
    z-index: 1;
    > img {
      width: 100%;
      height: 100%;
      cursor: pointer;
      border-radius: 50%;
    }
  }
  .leftArrow {
    left: -18px;
    > img {
      transform: rotate(180deg);
    }
  }
  .rightArrow {
    right: -18px;
  }
}

.emojiComponentWarp {
  padding-bottom: 0;
  :global {
    .linkflow-popover-inner-content {
      padding: 0;
    }
    .linkflow-popover-arrow {
      display: none;
    }
  }
}

.extendOperate {
  height: 40px;
  display: flex;
  align-items: center;
  padding: 0 6px;
  .line {
    width: 1px;
    height: 20px;
    margin: 0 6px 0 2px;
    background-color: var(--primary-background-color-5);
  }
  .operateItem {
    // width: 28px;
    // height: 28px;
    // &:first-child {
    //   position: relative;
    //   top: -3px;
    // }

    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 4px;
    cursor: pointer;
    margin-right: 4px;
    img {
      width: 28px;
      height: 28px;
      object-fit: contain;
      transition: transform 0.32s cubic-bezier(0.34, 1.56, 0.64, 1);
    }

    .imgLeft {
      &:hover {
        border-radius: 4px 0 0 4px;
        background: rgba(210, 210, 210, 42%);
      }
    }

    .arrowIcon {
      padding: 0 2px;
      width: 12px;

      &:hover {
        background: rgba(210, 210, 210, 42%);
        border-radius: 0 4px 4px 0;
      }
    }
    > span {
      height: 100%;
    }
  }
  .operateItem:hover {
    background: rgba(107, 107, 108, 8%);
  }

  .operateItemdisabled {
    cursor: not-allowed;
    opacity: 0.6;
  }
}
.selectedCommandContainer {
  // position: absolute;
  // top: 33%;
  // width: calc(100% - 48px);
  // background: #fff;
  display: flex;
  flex-direction: column;
  min-height: 120px;
  :global .linkflow-form {
    max-width: 100%;
    overflow: auto;
    // flex-wrap: nowrap;
    // margin: 0 0 5px 20px;
  }
  .selectedCommandBox {
    flex: 1 1;
    min-height: 0;
  }
  .selectedCommandFooter {
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 6px;
    .clearBtn {
      width: 28px;
      height: 28px;
      cursor: pointer;
      > img {
        width: 100%;
        height: 100%;
      }
      &:hover {
        background: var(--primary-background-color-8);
      }
    }
    .submitBtn {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 60px;
      height: 28px;
      background: var(--primary-text-color-9);
      border-radius: 6px;
      cursor: pointer;
      > img {
        width: 21px;
        height: 21px;
      }
    }
  }
  .describe {
    display: flex;
    align-items: center;
    height: 34px;
    background: var(--primary-background-color-8);
    border-radius: 8px 8px 0 0;
    padding: 0 12px;
    .name {
      font-size: 13px;
      color: var(--primary-text-color-1);
      line-height: 18px;
    }
    .desc {
      font-size: 13px;
      font-weight: 400;
      color: var(--primary-text-color-7);
      line-height: 18px;
    }
  }
  .selectedCommandContent {
    // max-height: 50%;
    // background: #fff;
    display: flex;
    // justify-content: space-between;
    padding: 10px 12px 9px;
    font-size: 15px;
    line-height: 22px;
    border-radius: 8px;
    .robotCommandBox {
      display: flex;
      align-items: center;
      font-size: 15px;
      font-weight: 600;
      color: var(--primary-text-color-1);
      line-height: 22px;
      > img {
        width: 30px;
        height: 30px;
        margin-right: 8px;
        border-radius: 8px;
      }
    }
    .btn {
      display: flex;
      align-items: center;
      height: 30px;
      font-size: 13px;
      font-weight: 600;
      color: var(--primary-text-color-9);
      line-height: 18px;
      cursor: pointer;
    }
  }
  .commandForm {
    :global {
      .linkflow-form-item {
        margin-bottom: 8px;
        margin-right: 12px;
      }
      .linkflow-input {
        padding: 3px 11px;
        font-size: 15px;
      }
    }
  }
}
.robotCommandListContainer {
  // :global .linkflow-modal-content {
  //   background-color: var(--primary-text-color-2);
  //   color: #fff;
  // }
  display: block;
  position: absolute;
  bottom: 100px;
  left: 0;
  background: #fff;
  width: 616px;
  border-radius: 10px;
  border: 1px solid var(--primary-background-color-5);
  z-index: 1;
  &:focus {
    outline: none;
  }
  .robotCommandList {
    max-height: 296px;
    padding: 4px 0;
    overflow: auto;

    .botGroup {
    }
    .promptTitle {
      padding: 8px 17px;
      font-size: 14px;
      color: var(--primary-text-color-3);
    }
    .prompt {
      padding: 12px 17px 24px;
    }

    .commandItem {
      display: flex;
      padding: 12px 17px;
      min-height: 40px;
      cursor: pointer;

      &:hover,
      &.active {
        background-color: var(--primary-background-color-15);
      }

      .commandContent {
        width: 100%;
      }

      .botName {
        width: 72px;
        display: flex;
        align-items: center;
        font-size: 12px;
        color: var(--primary-text-color-3);

        > div {
          width: 100%;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }

      .commandHeader {
        display: flex;
        align-items: center;
        gap: 8px;
      }

      .commandName {
        font-size: 14px;
        font-weight: 500;
        color: var(--primary-text-color-1);
        line-height: 20px;
      }

      .commandOptions {
        display: flex;
        gap: 4px;
        flex-wrap: wrap;
        .optionalNumbers {
          font-size: 12px;
          font-weight: 400;
          color: var(--primary-text-color);
          line-height: 17px;
          margin-left: 4px;
        }
      }

      .optionTag {
        font-size: 10px;
        padding: 1px 4px;
        color: var(--primary-text-color-1);
        background-color: var(--primary-background-color-18);
        border-radius: 4px;
        line-height: 14px;
      }

      .commandDescription {
        // margin-top: 4px;
        font-size: 12px;
        color: var(--primary-text-color-3);
        line-height: 18px;
      }
    }
  }
}

.filePopoverWarp {
  border-radius: 8px;
  padding-bottom: 0;
  background-color: var(--primary-background-color-6);
  .filePopoverContent {
    .item {
      width: 208px;
      height: 36px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 0 13px 0 18px;
      color: var(--primary-text-color-10);
      font-size: 14px;
      font-weight: 400;
      cursor: pointer;
      .desc {
        display: flex;
        align-items: center;
        height: 100%;
        > img {
          width: 18px;
          height: 18px;
          margin-right: 8px;
        }
      }
    }
    .item:hover {
      background: var(--primary-background-color-15);
      border-radius: 6px;
    }
  }
  :global {
    .linkflow-popover-inner {
      border-radius: 8px;
    }
    .linkflow-popover-inner-content {
      border-radius: 8px;
      padding: 6px;
      border: 1px solid #dddee0;
    }
    .linkflow-popover-arrow {
      display: none;
    }
  }
}

.cloudDocSelectPopoverWarp {
  border-radius: 8px;
  padding-left: 0;
  :global {
    .linkflow-popover-inner {
      border-radius: 8px;
      padding-bottom: 40px;
      background-color: transparent;
      box-shadow: none;
    }
    .linkflow-popover-inner-content {
      border-radius: 8px;
      padding: 0;
    }
    .linkflow-popover-arrow {
      display: none;
    }
  }
}

.autoResizeInput {
  display: flex;
  border-radius: 6px;
  border: 1px solid var(--primary-border-color);
  .label {
    flex-shrink: 0;
    font-size: 15px;
    font-weight: 600;
    color: var(--primary-text-color-1);
    line-height: 22px;
    padding: 3px 8px;
    border-right: 1px solid var(--primary-border-color);
    background: var(--file-backgroud-color);
    border-radius: 6px 0 0 6px;
  }
  .required {
    &::before {
      display: inline-block;
      margin-right: 4px;
      color: #ff4d4f;
      font-size: 14px;
      font-family: SimSun, sans-serif;
      line-height: 1;
      content: "*";
    }
  }
}
.autoResizeInputFocus {
  border: 1px solid #0074e2;
}
.autoResizeInputRrror {
  border: 1px solid #d6363f;
}

.textOperate {
  display: flex;
  align-items: center;
  padding: 0 6px;

  .operateItem {
    width: 28px;
    height: 28px;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 4px;
    margin-right: 4px;

    &.activeIcon {
      background: rgba(210, 210, 210, 42%);

      &:hover {
        background: rgba(210, 210, 210, 70%) !important;
      }
    }

    > div {
      width: 28px;
      height: 28px;
      display: flex;
      justify-content: center;
      align-items: center;
      cursor: pointer;
      border-radius: 4px;
    }

    > div:hover {
      background: rgba(107, 107, 108, 8%);
    }

    .check {
      background: rgba(210, 210, 210, 42%);
    }
  }

  .operateItemdisabled {
    cursor: not-allowed;
    opacity: 0.6;
  }

  .line {
    width: 1px;
    height: 20px;
    margin: 0 6px 0 2px;
    background-color: var(--primary-background-color-5);
  }
}

.emojiComponentWarpNew {
  ::-webkit-scrollbar {
    width: 6px; /* 设置竖向滚动条宽度为8px */
  }

  padding-bottom: 4px;

  :global {
    .linkflow-popover-inner {
      border-radius: 8px;
    }

    .linkflow-popover-arrow {
      display: none;
    }

    .linkflow-popover-inner-content {
      padding: 0;
    }

    .linkflow-popover-content {
      left: -7px;
    }
  }
  .emojiComponentWrapContent {
    width: 500px;
    height: 300px;
    border-radius: 8px;
    border: 1px solid #dddee0;
    display: flex;
    flex-direction: column;
  }

  .emojiNewEmojiTabContentArea {
    font-family: PingFangSC-Regular, lato, EmojiMart;
    flex: 1;
    border-radius: 8px 8px 0 0;
    overflow-y: auto;
    overflow-x: hidden;
    padding: 16px 16px 0;
  }
  .emojiNewEmojiTabContent {
    display: grid;
    grid-template-columns: repeat(15, 31px);
    cursor: pointer;
    width: 100%;
    // gap: 8px 6px;

    .emoji {
      width: 31px;
      height: 31px;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      position: relative;
      padding: 4px;

      &:hover {
        background-color: rgba(107, 107, 108, 8%);
        // border-radius: 50%;
        border-radius: 4px;
      }

      > div {
        font-size: 20px;
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }
  }

  .emojiNewContentArea {
    flex: 1;
    padding-top: 12px;
    border-radius: 8px 8px 0 0;
    overflow-y: auto;
    overflow-x: hidden;
  }
  .emojiNewContent {
    display: grid;
    grid-template-columns: repeat(6, 78px);
    width: 100%;
    justify-content: center;
    gap: 0 0;
  }
  .emojiNew {
    width: 78px;
    height: 78px;
    background: #ffffff;
    padding: 4px;

    &:hover {
      background-color: rgba(107, 107, 108, 8%);
      border-radius: 4px;
    }

    .imgContainer {
      width: 70px;
      height: 70px;
      border-radius: 4px;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      &.addFace {
        border: 1px dashed #dddee0;
        border-radius: 8px;
      }
    }

    img {
      max-width: 70px;
      max-height: 70px;
    }
  }

  .emojiNewFooter {
    width: 100%;
    height: 48px;
    background: #f6f6f7;
    border-radius: 0 0 8px 8px;
    display: flex;
  }

  .emojiNewFaceGroupsArea {
    flex: 1;
    display: flex;
    align-items: center;
    overflow: hidden;

    & > .emojiNewFooterFaceIcon {
      flex-shrink: 0;
    }

    &::-webkit-scrollbar {
      display: none; /* Chrome/Safari */
    }
  }

  .emojiNewFaceGroups {
    display: flex;
  }

  .emojiNewFooterFaceIcon {
    width: 56px;
    height: 48px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    img {
      width: 28px;
      height: 28px;
    }

    span {
      font-family: PingFangSC-Regular, lato, EmojiMart;
      font-size: 20px;
    }

    &.selected {
      background: #ffffff;
    }

    &.emojiNewBtnUnValid {
      pointer-events: none;
    }
  }

  .emojiSwitchBtn {
    img {
      border: 6px;

      &:hover {
        background-color: rgba(107, 107, 108, 8%);
      }
    }
  }
}

.rightButtonMenu {
  width: 150px;
  height: 48px;
  background: #ffffff;
  border-radius: 8px;
  box-shadow: 0 2px 4px 2px rgba(0, 0, 0, 4%);
  padding: 6px;
  display: flex;
  align-items: center;

  .userFaceMenu {
    width: 100%;
    height: 100%;
    border-radius: 6px;
    font-size: 14px;
    padding-left: 18px;
    color: #1d1c1d;
    cursor: pointer;
    display: flex;
    align-items: center;

    &:hover {
      background-color: rgba(107, 107, 108, 8%);
    }
  }
  // :global {
  //   .linkflow-dropdown-menu {
  //     padding: 6px;
  //   }

  //   .linkflow-dropdown-menu-item {
  //     padding: 0;
  //     font-size: 14px;
  //     // font-weight: 600;
  //     font-weight: 500;
  //     color: var(--primary-text-color-1);
  //     border-radius: 6px;
  //   }

  //   .linkflow-dropdown-menu-item-divider {
  //     margin: 3px 0;
  //     background-color: var(--primary-background-color-5);
  //   }
  // }
}

.imgLoading {
  /* 定义旋转的关键帧 */
  @keyframes rotate {
    from {
      transform: rotate(0deg);
    }

    to {
      transform: rotate(360deg);
    }
  }

  animation: rotate 1s linear infinite;
}

.defaultEmojiArea {
  display: flex;
  max-width: 120px;
  max-height: 120px;
}
