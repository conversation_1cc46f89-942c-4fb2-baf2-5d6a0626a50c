import { FC, memo } from 'react';
import { MessageItem as MessageItemType } from '@ht/openim-wasm-client-sdk';
import { updateOneMessage } from '@/hooks/useHistoryMessageList';
import UserInfoRender from './UserInfoRender';
import styles from './index.less';
import { IMessageItemProps } from '.';

const FaceMessageRender: FC<IMessageItemProps> = ({ ...props }) => {
  const { message } = props;
  const { faceElem } = message;

  const calcBestWidthHeight = (
    width: number | undefined,
    height: number | undefined
  ) => {
    const defaultSize = { width: 120, height: 120 };

    // 无效输入直接返回默认值
    if (!width || !height || width <= 0 || height <= 0) {
      return defaultSize;
    }

    const maxSide = Math.max(width, height);

    let targetWidth = width;
    let targetHeight = height;

    if (maxSide > 120) {
      // 等比缩小到最长边 120
      const ratio = 120 / maxSide;
      targetWidth = width * ratio;
      targetHeight = height * ratio;
    } else if (maxSide < 75) {
      // 等比放大到最长边 75
      const ratio = 75 / maxSide;
      targetWidth = width * ratio;
      targetHeight = height * ratio;
    }

    // 四舍五入并确保最小为 1px
    return {
      width: Math.max(1, Math.round(targetWidth)),
      height: Math.max(1, Math.round(targetHeight)),
    };
  };

  return (
    <div className={styles.faceEmojiWrap}>
      <UserInfoRender {...props}>
        <div>
          <div
            style={{
              ...calcBestWidthHeight(faceElem?.faceWidth, faceElem?.faceHeight),
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
            }}
          >
            <img
              className={styles.showPicture}
              style={{ cursor: 'auto' }}
              src={faceElem?.faceURL}
              onError={() => {
                updateOneMessage({
                  ...message,
                  isFaceUrlInValid: true,
                } as MessageItemType);
              }}
            />
          </div>
        </div>
      </UserInfoRender>
    </div>
  );
};

export default memo(FaceMessageRender);
