import React, { FC, useMemo } from 'react';
import { useSendMessage } from '@/hooks/useSendMessage';
import useUserInfo from '@/hooks/useUserInfo';
import { IMSDK } from '@/layouts/BasicLayout';
import { useConversationStore } from '@/store';
import { ConversationItem } from '@ht/openim-wasm-client-sdk';
import arrowIcon from '@/assets/stream/arrow.png';
import styles from './index.less';

interface Question {
  text: string;
  handleClick?: () => void;
}

interface GuideQuestionsProps {
  questions: Question[] | string[];
  currentConversation?: ConversationItem;
}

const GuideQuestions: FC<GuideQuestionsProps> = ({
  questions,
  currentConversation,
}) => {
  const currentMultiSession = useConversationStore(
    (state) => state.currentMultiSession
  );
  const llmLoading = useConversationStore((state) => state.llmLoading);
  const { sendStreamMessage } = useSendMessage(currentMultiSession);
  const { userDetail } = useUserInfo(currentConversation?.userID);

  const btnClick = async (value: string) => {
    if (llmLoading) {
      return;
    }
    const message = (await IMSDK.createTextMessage(value)).data;
    sendStreamMessage({
      recvUser: userDetail,
      curMsg: message,
      // 移除不在streamMessageProps类型中的属性
      lastMsg: undefined,
      curConversation: currentMultiSession,
    });
  };
  // 为每个问题项生成延迟时间
  const questionsWithDelay = useMemo(() => {
    return questions.map((q, idx) => ({
      content: typeof q === 'string' ? q : q.text,
      delay: idx * 0.2, // 每个问题延迟0.2秒
    }));
  }, [questions]);

  return (
    <div className={styles.guideQuestions}>
      {questionsWithDelay.map((item, idx) => {
        const questionText = item.content;
        return (
          <div
            key={idx}
            className={styles.itemCard}
            onClick={() => btnClick(questionText)}
            style={{ animationDelay: `${item.delay}s` }}
          >
            {questionText}
            <img src={arrowIcon} className={styles.arrowIcon} />
          </div>
        );
      })}
    </div>
  );
};

export default GuideQuestions;
