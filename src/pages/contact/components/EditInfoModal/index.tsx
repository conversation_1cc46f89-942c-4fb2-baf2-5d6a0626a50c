import React, { useState } from 'react';
import { PlusOutlined } from '@ht-icons/sprite-ui-react';
import { Upload, message, Modal, Radio } from '@ht/sprite-ui';
import { RcFile } from '@ht/sprite-ui/lib/upload';
import closeIcon from '@/assets/channel/close.png';
import classnames from 'classnames';
import { useUserStore } from '@/store';
import { feedbackToast } from '@/utils/common';
import { getRandomDefaultAvatar } from '@/utils/avatar';
import { IMSDK } from '@/layouts/BasicLayout';
import { t } from 'i18next';
import OIMAvatar from '@/components/OIMAvatar';
import { UserDetailExProps } from '@/store/type';
import styles from './index.less';

interface EditInfoModalProps {
  onClose: () => void;
}
const getBase64 = (img: RcFile, callback: (url: string) => void) => {
  const reader = new FileReader();
  reader.addEventListener('load', () => callback(reader.result as string));
  reader.readAsDataURL(img);
};
const EditInfoModal: React.FC<EditInfoModalProps> = ({ onClose }) => {
  const { selfInfo, updateSelfInfo } = useUserStore.getState();

  const [selfFaceUrl, setSelfFaceUrl] = useState<any>('');

  const uploadButton = (
    <div>
      <PlusOutlined />
      <div style={{ marginTop: 8 }}>Upload</div>
    </div>
  );

  const renderInfo = (
    label: string,
    value: string,
    style?: React.CSSProperties
  ) => {
    return (
      <div className={styles.nameArea} style={{ ...style }}>
        <span className={styles.label}>{label}</span>
        <div className={classnames(styles.info, styles.border)}>
          <span>{value}</span>
        </div>
      </div>
    );
  };

  const uploadCustomRequest = async (file: any) => {
    try {
      const isJpgOrPng =
        file.type === 'image/jpeg' ||
        file.type === 'image/png' ||
        file.type === 'image/jpg';
      if (!isJpgOrPng) {
        message.error('请上传jpeg、png、jpg格式的图片文件');
      }

      getBase64(file as RcFile, (url) => {
        setSelfFaceUrl(url);
        updateSelfInfo({
          faceURL: url,
        });
      });

      const uploadFileResult = await IMSDK.uploadFile({
        name: file.name,
        contentType: file.type,
        uuid: file.uuid,
        cause: '修改头像',
        file,
      });

      await IMSDK.setSelfInfo({ faceURL: uploadFileResult?.data?.url }).catch(
        (error) => {
          console.error('修改头像失败', error);
          feedbackToast({ error, msg: t('toast.updateAvatarFailed') });
        }
      );

      setSelfFaceUrl(uploadFileResult?.data?.url);
      updateSelfInfo({
        faceURL: uploadFileResult?.data?.url,
      });

      feedbackToast({ msg: t('toast.updateAvatarSuccess') });
    } catch (error: any) {
      feedbackToast({ error, msg: t('toast.updateAvatarFailed') });
      updateSelfInfo({
        faceURL: selfInfo.faceURL,
      });
    }
  };

  const showDetailDataEX = JSON.parse(
    selfInfo?.ex || '{}'
  ) as UserDetailExProps;

  return (
    <Modal
      title={null}
      footer={null}
      closable={false}
      centered={true}
      wrapClassName={styles.modalContainer}
      width={700}
      open={true}
      maskClosable={false}
      keyboard={true}
    >
      <div className={styles.container}>
        <div className={styles.header}>
          {/* <div className={styles.title}>编辑你的个人档案</div> */}
          <div className={styles.title}>个人档案</div>
          <img src={closeIcon} onClick={() => onClose()} />
        </div>

        <div className={styles.content}>
          <div className={styles.left}>
            {renderInfo('全名', selfInfo.nickname)}
            {/* {renderInfo('别名', selfInfo.nickname)} */}

            <div className={styles.nameArea}>
              <span className={styles.label}>性别</span>
              <div className={styles.info}>
                <Radio.Group size="large" value={showDetailDataEX.gender}>
                  <Radio value={1} disabled={true}>
                    男
                  </Radio>
                  <Radio value={2} disabled={true}>
                    女
                  </Radio>
                </Radio.Group>
              </div>
            </div>

            {renderInfo('手机号', showDetailDataEX.mobile || '无')}
            {renderInfo('座机', showDetailDataEX.landline || '无')}
            {renderInfo('邮箱', showDetailDataEX.mail || '无')}
            {/* {renderInfo('直属上级', '直属上级-暂时没有')}
            {renderInfo('职务', '职务-暂时没有')} */}
            {renderInfo('工号', showDetailDataEX.employeeCode)}
            {/* {renderInfo('备注', selfInfo.phoneNumber, {
              position: 'absolute',
              width: '694px',
            })} */}
          </div>
          <div className={styles.right}>
            <div className={styles.label}>个人档案图片</div>
            <div className={styles.imgContainer}>
              {selfFaceUrl ? (
                <img
                  src={selfFaceUrl}
                  onLoad={(e) => {
                    if (!(e.target as any).inited) {
                      (e.target as any).inited = true;
                      (e.target as any).src = selfFaceUrl;
                    }
                  }}
                  onError={(e) => {
                    (e.target as any).src = getRandomDefaultAvatar(
                      selfInfo.userID
                    );
                  }}
                  style={{ width: '100%', height: '100%' }}
                />
              ) : (
                <OIMAvatar
                  userID={selfInfo.userID}
                  hideOnlineStatus={true}
                  size={192}
                />
              )}

              <Upload
                accept={'.jpg,.jpeg,.png'}
                listType="picture"
                showUploadList={false}
                customRequest={(file) => uploadCustomRequest(file.file)}
              >
                <div className={styles.uploadBtn}>上传照片</div>
              </Upload>
            </div>
          </div>
        </div>
        <div className={styles.footer}>
          {/* <div className={styles.footerBtn}>保存修改</div> */}
          <div className={styles.footerBtn} onClick={() => onClose()}>
            取消
          </div>
        </div>
      </div>
    </Modal>
  );
};

export default EditInfoModal;
