{"name": "linkflow", "version": "0.1.0", "private": true, "description": "基于openIM的即时通讯应用", "keywords": ["铃客", "即时通讯", "openIM"], "author": "015754", "repository": "***************************:mss/linkflow.git", "scripts": {"analyze": "cross-env BUNDLE_ANALYZE=true oula build", "build": "cross-env REACT_APP_ENV=prod oula build", "dev": "cross-env REACT_APP_ENV=dev oula dev", "postinstall": "patch-package && oula g tmp", "lint": "oula g tmp && npm run lint:js && npm run lint:style", "lint-staged:js": "eslint --ext .js,.jsx,.ts,.tsx ", "lint:fix": "eslint --fix --cache --ext .js,.jsx,.ts,.tsx ./src && npm run lint:style", "lint:js": "eslint --cache --ext .js,.jsx,.ts,.tsx ./src", "lint:style": "stylelint --fix \"src/**/*.less\"", "start": "cross-env REACT_APP_ENV=dev oula dev", "start:sit": "cross-env REACT_APP_ENV=sit oula dev", "sit": "cross-env REACT_APP_ENV=sit oula dev", "start:debug": "cross-env DEBUG=builder oula dev", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "tsc": "tsc --noEmit", "eslint:output": "eslint --fix -f html > ./eslint-error.html", "prepare": "husky install && node ./.husky/prepare.js &&  husky install && node ./.husky/prepare.js"}, "dependencies": {"@ant-design/pro-utils": "^1.19.5", "@antv/data-set": "^0.11.0", "@antv/l7": "^2.1.9", "@antv/l7-maps": "^2.1.9", "@antv/l7-react": "^2.1.9", "@emoji-mart/data": "^1.2.1", "@emoji-mart/react": "^1.1.1", "@fsegurai/codemirror-theme-github-light": "^6.1.4", "@ht-icons/sprite-ui-react": "^0.3.4", "@ht/advanced-table": "^0.5.5", "@ht/openim-electron-client-sdk": "0.13.0-beta.3", "@ht/openim-wasm-client-sdk": "0.16.0-beta.5", "@ht/sprite-ui": "^1.3.0-beta.10", "@ht/xlog": "^3.4.0", "@jlongster/sql.js": "^1.6.7", "@lowcode/lc-render": "^1.2.4", "@milkdown/kit": "7.6.3", "@milkdown/react": "7.6.3", "@mui/material": "^6.4.0", "@prosemirror-adapter/react": "^0.4.0", "@sendbird/chat": "^4.16.3", "@tauri-apps/api": "^2.3.0", "@tauri-apps/plugin-clipboard-manager": "^2.2.1", "@tauri-apps/plugin-deep-link": "^2.2.0", "@tauri-apps/plugin-global-shortcut": "^2.2.0", "@tauri-apps/plugin-opener": "^2.2.6", "@tauri-apps/plugin-os": "^2.2.1", "@tauri-apps/plugin-process": "^2.2.0", "@types/lodash.debounce": "^4.0.6", "@types/lodash.isequal": "^4.5.5", "@types/mark.js": "^8.11.12", "@types/react-router-dom": "^5.3.3", "@uiw/codemirror-theme-material": "^4.23.10", "@umijs/route-utils": "^1.0.33", "absurd-sql-optimized": "^0.0.1", "ahooks": "^3.8.4", "bizcharts": "^3.5.3-beta.0", "bizcharts-plugin-slider": "^2.1.1-beta.1", "classnames": "^2.2.6", "copy-to-clipboard": "^3.3.3", "dva": "^2.4.0", "emoji-mart": "^5.6.0", "emoji-picker-react": "^4.12.0", "js-web-screen-shot": "^1.9.8-rc.1", "lodash": "^4.17.11", "lodash-decorators": "^6.0.0", "lodash.debounce": "^4.0.8", "lodash.isequal": "^4.5.0", "lru-cache": "^6.0.0", "mark.js": "^8.11.1", "mockjs": "^1.0.1-beta3", "moment": "^2.25.3", "numeral": "^2.0.6", "nzh": "^1.0.3", "omit.js": "^2.0.2", "patch-package": "^8.0.0", "pcm-player": "^0.0.18", "prop-types": "^15.5.10", "react": "18", "react-color": "^2.19.3", "react-dev-inspector": "^1.1.1", "react-dom": "18", "react-fittext": "^1.0.0", "react-helmet-async": "^1.0.4", "react-highlight-words": "^0.21.0", "react-i18next": "^15.4.0", "react-infinite-scroll-component": "^6.1.0", "react-intersection-observer": "^9.16.0", "react-json-pretty": "^2.2.0", "react-router-dom": "5.2.0", "remark-gfm": "4.0.1", "umi-request": "^1.0.8", "zustand": "4.3.3"}, "devDependencies": {"@babel/core": "^7.23.9", "@babel/preset-env": "^7.23.9", "@babel/preset-react": "^7.23.3", "@babel/preset-typescript": "^7.23.3", "@ht/eslint-config-htsc": "^2.0.14", "@ht/react-virtuoso": "0.4.0", "@ht/umi-plugin-hashprefix": "^0.1.4", "@ht/webpack-plugin-concat-jobid": "^2.0.3", "@livekit/components-react": "^2.7.0", "@modern-js-app/eslint-config": "1.2.4", "@oula/oula": "^1.1.3", "@oula/plugin-xlog": "^1.1.2", "@oula/preset-react-pc": "^3.0.4", "@prosemirror-adapter/react": "^0.4.0", "@types/classnames": "^2.2.7", "@types/express": "^4.17.0", "@types/history": "^4.7.2", "@types/jest": "^29.5.14", "@types/lodash": "^4.14.144", "@types/lru-cache": "^5.1.1", "@types/md5": "^2.3.5", "@types/react": "^17.0.0", "@types/react-color": "^3.0.6", "@types/react-helmet": "^6.1.0", "@typescript-eslint/eslint-plugin": "^4.33.0", "@typescript-eslint/parser": "^5", "babel-jest": "^29.7.0", "carlo": "^0.9.46", "chalk": "^4.0.0", "copy-webpack-plugin": "^9.0.1", "cross-env": "^7.0.0", "cross-port-killer": "^1.1.1", "detect-installer": "^1.0.1", "emoji-picker-element": "^1.26.0", "enzyme": "^3.11.0", "eslint": "^7.32.0", "eslint-config-airbnb": "^18.2.1", "eslint-config-prettier": "^8.3.0", "eslint-import-resolver-webpack": "^0.13.1", "eslint-plugin-eslint-comments": "^3.1.1", "eslint-plugin-filenames": "^1.3.2", "eslint-plugin-import": "^2.18.2", "eslint-plugin-jsx-a11y": "^6.4.1", "eslint-plugin-markdown": "^2.2.0", "eslint-plugin-node": "^11.1.0", "eslint-plugin-prettier": "^3.4.1", "eslint-plugin-promise": "^5.1.0", "eslint-plugin-react": "^7.24.0", "eslint-plugin-react-hooks": "^4.2.0", "gh-pages": "^3.0.0", "husky": "7.0.4", "i18next": "^24.2.1", "jest": "30.0.2", "jest-environment-node": "30.0.2", "jsdom-global": "^3.0.2", "lint-staged": "^11.2.2", "localforage": "^1.10.0", "md5": "^2.3.0", "milkdown-mentions-plugin": "^0.0.2", "mitt": "^3.0.1", "mockjs": "^1.0.1-beta3", "postcss": "^8.4.16", "postcss-html": "^1.5.0", "postcss-less": "^6.0.0", "prettier": "^2.7.1", "puppeteer-core": "^7.0.1", "rc-virtual-list": "^3.18.1", "react-image-file-resizer": "^0.4.8", "react-query": "^3.39.3", "react-resizable-panels": "^2.1.7", "react-use": "^17.6.0", "stylelint": "^14.9.1", "stylelint-config-css-modules": "^4.1.0", "stylelint-config-prettier": "^9.0.3", "stylelint-config-standard": "^26.0.0", "stylelint-declaration-block-no-ignored-properties": "^2.5.0", "stylelint-prettier": "^2.0.0", "twemoji": "^14.0.2", "typescript": "^5", "uuid": "^11.0.5"}, "browserslist": ["chrome >= 87", "edge >= 88", "firefox >= 78", "safari >= 14"], "checkFiles": ["src/**/*.js*", "src/**/*.ts*", "src/**/*.less", "config/**/*.js*", "scripts/**/*.js"], "engines": {"node": ">=14.0.0"}, "templateConfig": {"name": "template-pc", "version": "1.0.0"}}