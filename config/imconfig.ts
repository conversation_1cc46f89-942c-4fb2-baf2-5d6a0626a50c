
export const AXIOSURL = "http://************/openim/api"

// export const IMWsAddr = (location.host.indexOf('localhost') >-1? `${location.protocol.replace('http','ws')}//***********:10001` : `${location.protocol.replace('http','ws')}//${location.host}/openimws`)
export const IMWsAddr = `${location.protocol.replace('http','ws')}//${location.host}/linkFlowService/openimws`
export const IMApiAddr = `${location.protocol}//${location.host}/linkFlowService/openim`

export const platformID = 5

export const coreWasmPath = () => {
    const href = location.href
    const sdkVersion = process?.env?.npm_package_dependencies__ht_openim_wasm_client_sdk || ''

    if (process.env.REACT_APP_ENV === 'sit') return '/PortalStatics/openIM.wasm?version='+sdkVersion;
    else if (href.indexOf('localhost') >=0 || href.indexOf('127.0.0.1')>=0 ) {
        return '/PortalStatics/openIM.dev.wasm?version='+sdkVersion;
    } else if (href.indexOf('eipdev') >= 0) {
        return '/PortalStatics/openIM.dev.wasm?version='+sdkVersion;
    }
    else {
        return '/PortalStatics/openIM.wasm?version='+sdkVersion;
    }
}

export const sqlWasmPath = `/PortalStatics/sql-wasm.wasm`
