diff --git a/node_modules/micromark-extension-gfm-autolink-literal/dev/lib/syntax.js b/node_modules/micromark-extension-gfm-autolink-literal/dev/lib/syntax.js
index 904e272..23cf0ed 100644
--- a/node_modules/micromark-extension-gfm-autolink-literal/dev/lib/syntax.js
+++ b/node_modules/micromark-extension-gfm-autolink-literal/dev/lib/syntax.js
@@ -653,8 +653,8 @@ function tokenizePath(effects, ok) {
       code === codes.lessThan ||
       code === codes.questionMark ||
       code === codes.rightSquareBracket ||
-      code === codes.underscore ||
+      // code === codes.underscore ||
+      code === codes.backslash ||
       code === codes.tilde
     ) {
       return effects.check(trail, ok, pathAtPunctuation)(code)
@@ -733,8 +733,8 @@ function tokenizeTrail(effects, ok, nok) {
       code === codes.colon ||
       code === codes.semicolon ||
       code === codes.questionMark ||
-      code === codes.underscore ||
+      // code === codes.underscore ||
+      code === codes.backslash ||
       code === codes.tilde
     ) {
       effects.consume(code)
diff --git a/node_modules/micromark-extension-gfm-autolink-literal/lib/syntax.js b/node_modules/micromark-extension-gfm-autolink-literal/lib/syntax.js
index dffbaf1..50c6f7f 100644
--- a/node_modules/micromark-extension-gfm-autolink-literal/lib/syntax.js
+++ b/node_modules/micromark-extension-gfm-autolink-literal/lib/syntax.js
@@ -581,7 +581,7 @@ function tokenizePath(effects, ok) {
     // Check whether this trailing punctuation marker is optionally
     // followed by more trailing markers, and then followed
     // by an end.
-    if (code === 33 || code === 34 || code === 38 || code === 39 || code === 41 || code === 42 || code === 44 || code === 46 || code === 58 || code === 59 || code === 60 || code === 63 || code === 93 || code === 95 || code === 126) {
+    if (code === 33 || code === 34 || code === 38 || code === 39 || code === 41 || code === 42 || code === 44 || code === 46 || code === 58 || code === 59 || code === 60 || code === 63 || code === 92 || code === 93 || code === 126) {
       return effects.check(trail, ok, pathAtPunctuation)(code);
     }
     if (code === null || markdownLineEndingOrSpace(code) || unicodeWhitespace(code)) {
@@ -641,7 +641,7 @@ function tokenizeTrail(effects, ok, nok) {
    */
   function trail(code) {
     // Regular trailing punctuation.
-    if (code === 33 || code === 34 || code === 39 || code === 41 || code === 42 || code === 44 || code === 46 || code === 58 || code === 59 || code === 63 || code === 95 || code === 126) {
+    if (code === 33 || code === 34 || code === 39 || code === 41 || code === 42 || code === 44 || code === 46 || code === 58 || code === 59 || code === 63 || code === 92 || code === 126) {
       effects.consume(code);
       return trail;
     }
